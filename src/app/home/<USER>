"use client";

import Link from "next/link";

import MainLayout from "@/components/layout/MainLayout";
import Banner from "@/components/organisms/banner";
import CardFeature from "@/components/organisms/card-feature";
import {
  ExamIcon,
  FormIcon,
  HistoryIcon,
  LessonPlanIcon,
  PenIcon,
  SlideIcon,
} from "@/constants/icon";
import ItemSection from "@/components/organisms/item-section";
import HistoryCard from "@/components/organisms/history-card";
import HistoryList from "@/components/organisms/history-list";
import { useSearchParams } from "next/navigation";
import { useBookTypesService } from "@/services/bookTypeServices";
import SpotlightCard from "@/components/ui/SpotlightCard";
import GridDistortion from "@/components/organisms/banner/GridDistortion";

export default function Home() {
  const searchParams = useSearchParams();
  const view = searchParams.get("view") || "grid";
  const { data: bookTypes } = useBookTypesService();

  // <PERSON><PERSON> liệu điể<PERSON> đến đ<PERSON> AI khuyên dùng
  const aiRecommendedDestinations = [
    {
      id: 1,
      name: "Kyoto",
      description:
        "<PERSON><PERSON><PERSON><PERSON> AI đề xuất cho người yêu thích văn hóa truyền thống và nghệ thuật",
      matchScore: 97,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth={1.5}
          stroke="currentColor"
          className="w-8 h-8"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z"
          />
        </svg>
      ),
    },
    {
      id: 2,
      name: "Barcelona",
      country: "Tây Ban Nha",
      description:
        "Phù hợp với các nhà thám hiểm đô thị thích kiến trúc và ẩm thực",
      matchScore: 95,
      imageUrl: "/images/barcelona.jpg", // Thay bằng đường dẫn thực tế
    },
    {
      id: 3,
      name: "Bali",
      country: "Indonesia",
      description:
        "Điểm đến cân bằng giữa thư giãn và phiêu lưu cho người thích khám phá",
      matchScore: 94,
      imageUrl: "/images/bali.jpg", // Thay bằng đường dẫn thực tế
    },
    {
      id: 4,
      name: "Cape Town",
      country: "Nam Phi",
      description:
        "Sự kết hợp hoàn hảo của phong cảnh thiên nhiên và văn hóa đô thị sôi động",
      matchScore: 92,
      imageUrl: "/images/capetown.jpg", // Thay bằng đường dẫn thực tế
    },
  ];

  const getRandomColorClass = () => {
    const colorClasses = [
      "text-teal-300",
      "text-gray-600",
      "text-green-300",
      "text-blue-500",
      "text-gray-600",
      "text-violet-400",
      "text-cyan-300",
      "text-gray-600",
      "text-gray-600",
      "text-rose-700",
      "text-pink-600",
      "text-gray-600",
    ];
    const randomIndex = Math.floor(Math.random() * colorClasses.length);
    return colorClasses[randomIndex];
  };

  return (
    <MainLayout>
      {/* <Banner /> */}
      <GridDistortion
        imageSrc="/images/background/abstract-bg.svg"
        grid={10}
        mouse={0.1}
        strength={0.15}
        relaxation={0.9}
        className="rounded-2xl"
      />

      <section className="grid grid-cols-1 lg:grid-cols-5 md:grid-cols-3 sm:grid-cols-2 gap-5">
        {bookTypes?.data?.content
          ?.sort((a: any, b: any) => a.priority - b.priority)
          ?.map((feature: any) => (
            <CardFeature
              key={feature.id}
              icon={feature.icon}
              title={feature.name}
              description={feature.description}
              href={feature.href}
            />
          ))}
      </section>

      <section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-5">
        <SpotlightCard
          className="!p-0 !bg-transparent !border-0 w-full aspect-[4/3] rounded-lg overflow-hidden"
          spotlightColor="rgba(59, 130, 246, 0.3)"
        >
          <img
            src="/images/background/LessonPlanCreation.svg"
            className="w-full h-full object-cover"
          />
        </SpotlightCard>
        <SpotlightCard
          className="!p-0 !bg-transparent !border-0 w-full aspect-[4/3] rounded-lg overflow-hidden"
          spotlightColor="rgba(34, 197, 94, 0.3)"
        >
          <img
            src="/images/background/ExamCreation.svg"
            className="w-full h-full object-cover"
          />
        </SpotlightCard>
        <SpotlightCard
          className="!p-0 !bg-transparent !border-0 w-full aspect-[4/3] rounded-lg overflow-hidden"
          spotlightColor="rgba(168, 85, 247, 0.3)"
        >
          <img
            src="/images/background/SlideCreation.svg"
            className="w-full h-full object-cover"
          />
        </SpotlightCard>
      </section>

      <ItemSection
        title={
          <>
            {HistoryIcon}
            Lịch sử
          </>
        }
      />
      {view === "list" ? (
        <HistoryList />
      ) : (
        <section className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-5">
          {Array.from({ length: 7 }).map((_, index) => (
            <HistoryCard key={index} className={getRandomColorClass()} />
          ))}
        </section>
      )}
    </MainLayout>
  );
}
