"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { FileUp, Plus, FileText } from "lucide-react";
import ExamFileImport from "../exam-file-import";

interface ExamTemplateSelectorProps {
  onImportFile: (files: File[]) => void;
  onCreateManually: () => void;
  isImporting?: boolean;
}

export default function ExamTemplateSelector({
  onImportFile,
  onCreateManually,
  isImporting = false,
}: ExamTemplateSelectorProps) {
  const [selectedMode, setSelectedMode] = useState<
    "none" | "import" | "manual"
  >("none");

  const handleModeSelect = (mode: "import" | "manual") => {
    setSelectedMode(mode);
  };

  const handleFileSubmit = (files: File[]) => {
    onImportFile(files);
  };

  const handleBackToSelection = () => {
    setSelectedMode("none");
  };

  // Show mode selection when no mode is selected
  if (selectedMode === "none") {
    return (
      <div className="w-full max-w-7xl h-[45vh] mx-auto p-6">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-calsans mb-2">Tạo Template Đề Thi</h1>
          <p className="text-gray-600">
            Chọn cách tạo template đề thi mới. Template có thể được sử dụng
            nhiều lần để tạo các đề thi khác nhau.
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-6 h-full">
          {/* Import from file option */}
          <div
            className="overflow-hidden relative rounded-lg p-6  hover:shadow-md transition-all cursor-pointer flex flex-col items-center text-center aspect-[4/3] w-full md:w-auto md:flex-1"
            onClick={() => handleModeSelect("import")}
          >
            {/* <h2 className="text-lg font-semibold mb-2 z-10">
              Import từ file DOCX
            </h2> */}
            <img
              src={"/images/background/import.svg"}
              className="absolute inset-0 w-full h-full object-cover z-0"
            />
          </div>

          {/* Create manually option */}
          <div
            className="overflow-hidden relative rounded-lg p-6 hover:shadow-md transition-all cursor-pointer flex flex-col items-center text-center aspect-[4/3] w-full md:w-auto md:flex-1"
            onClick={() => handleModeSelect("manual")}
          >
            {/* <h2 className="text-lg font-semibold mb-2 z-10">
              Import từ file DOCX
            </h2> */}
            <img
              src={"/images/background/manual.svg"}
              className="absolute inset-0 w-full h-full object-cover z-0"
            />
          </div>
        </div>
      </div>
    );
  }

  // Show file import interface when import mode is selected
  if (selectedMode === "import") {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <div className="mb-4">
          <Button
            variant="ghost"
            onClick={handleBackToSelection}
            className="mb-4"
          >
            ← Quay lại
          </Button>
          <h2 className="text-xl font-semibold">
            Import Template từ File DOCX
          </h2>
        </div>

        <ExamFileImport onSubmit={handleFileSubmit} isLoading={isImporting} />
      </div>
    );
  }

  // Show manual creation interface when manual mode is selected
  if (selectedMode === "manual") {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <div className="mb-4">
          <Button
            variant="ghost"
            onClick={handleBackToSelection}
            className="mb-4"
          >
            ← Quay lại
          </Button>
          <h2 className="text-xl font-semibold">Tạo Template Thủ Công</h2>
        </div>

        <div className="text-center p-8">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="mb-6">
            Bắt đầu tạo template đề thi mới với các câu hỏi và phần thi tùy
            chỉnh
          </p>
          <Button onClick={onCreateManually}>Tiếp tục</Button>
        </div>
      </div>
    );
  }

  return null;
}
